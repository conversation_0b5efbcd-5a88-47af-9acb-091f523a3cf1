package com.tqhit.battery.one.features.stats.corebattery.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.features.stats.corebattery.data.CoreBatteryStatus
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Android Service that continuously monitors battery status changes and provides
 * raw battery data through CoreBatteryStatsProvider.
 * 
 * This service runs in the foreground to ensure reliability and registers
 * a BroadcastReceiver for ACTION_BATTERY_CHANGED intents.
 */
@AndroidEntryPoint
class CoreBatteryStatsService : Service() {
    

    
    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    @Inject
    lateinit var appRepository: AppRepository

    private lateinit var batteryManager: BatteryManager
    private var batteryEventReceiver: BroadcastReceiver? = null

    // Cache for formatted notification content to reduce CPU usage
    private var lastFormattedContent: String? = null
    private var lastContentHash: Int = 0
    private var lastNotificationUpdateTime = 0L

    companion object {
        private const val TAG = "CoreBatteryStatsService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "unified_battery_monitor_channel"
        private const val CHANNEL_NAME = "Unified Battery Monitor"

        // Intent actions for controlling the service
        const val ACTION_START_MONITORING = "com.tqhit.battery.one.ACTION_START_CORE_BATTERY_MONITORING"
        const val ACTION_STOP_MONITORING = "com.tqhit.battery.one.ACTION_STOP_CORE_BATTERY_MONITORING"

        // Notification update interval for real-time feel (5 seconds)
        private const val NOTIFICATION_UPDATE_INTERVAL_MS = 5000L

        // Optimization: Cache formatted strings to reduce CPU usage
        private const val DECIMAL_FORMAT_PATTERN = "%.1f"
    }
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "CoreBatteryStatsService created")
        
        // Initialize BatteryManager for getting battery properties
        batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        
        // Create notification channel for Android O+
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "onStartCommand called with action: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_MONITORING -> {
                Log.d(TAG, "Starting core battery monitoring")
                startForeground(NOTIFICATION_ID, createServiceNotification(null))
                startMonitoring()
            }
            ACTION_STOP_MONITORING -> {
                Log.d(TAG, "Stopping core battery monitoring")
                stopMonitoringAndService()
            }
            else -> {
                Log.d(TAG, "Starting with default action")
                startForeground(NOTIFICATION_ID, createServiceNotification(null))
                startMonitoring()
            }
        }
        
        // Return START_STICKY to restart the service if it gets killed
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        // This service doesn't support binding
        return null
    }
    
    override fun onDestroy() {
        Log.d(TAG, "CoreBatteryStatsService destroyed")
        stopMonitoringAndService()
        super.onDestroy()
    }
    
    /**
     * Checks if the battery event receiver is currently registered.
     *
     * @return true if receiver is active, false otherwise
     */
    fun isReceiverRegistered(): Boolean {
        return batteryEventReceiver != null
    }
    
    /**
     * Creates the notification channel for unified battery monitoring on Android O+.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Unified battery monitoring with charging speed, temperature, percentage, and time estimation"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)

            Log.d(TAG, "Unified notification channel created: $CHANNEL_ID")
        }
    }
    
    /**
     * Creates the unified foreground service notification with comprehensive battery information.
     * Includes charging speed, temperature, battery percentage, charging status, and smart time estimation.
     *
     * @param status Current battery status to display in notification (optional)
     * @return Notification object for the foreground service
     */
    private fun createServiceNotification(status: CoreBatteryStatus?): Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )

        // Always try to get current status if none provided
        val currentStatus = status ?: coreBatteryStatsProvider.getCurrentStatus()

        val contentText = if (currentStatus != null) {
            getFormattedUnifiedNotificationContent(currentStatus)
        } else {
            "Initializing battery monitoring..."
        }

        Log.d(TAG, "Creating notification with content: $contentText")

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Unified Battery Monitor")
            .setContentText(contentText)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }

    /**
     * Formats unified notification content with comprehensive battery information.
     * Includes caching to reduce CPU usage and smart time estimation.
     */
    private fun getFormattedUnifiedNotificationContent(status: CoreBatteryStatus): String {
        // Create a hash of the current status for cache comparison
        val currentHash = status.hashCode()

        // Return cached content if status hasn't changed
        if (currentHash == lastContentHash && lastFormattedContent != null) {
            return lastFormattedContent!!
        }

        // Calculate metrics using optimized approach
        val amperage = status.currentMicroAmperes / 1000.0f
        val power = calculatePowerWatts(status.currentMicroAmperes, status.voltageMillivolts)
        val temperature = status.temperatureCelsius

        // Format unified content with comprehensive battery information
        val content = buildString {
            // Battery percentage and charging status
            append("${status.percentage}% • ")
            append(if (status.isCharging) "Charging" else "Discharging")
            append(" • ")

            // Current charging speed
            append(String.format(DECIMAL_FORMAT_PATTERN, kotlin.math.abs(amperage)))
            append(" mA • ")

            // Temperature
            append(String.format(DECIMAL_FORMAT_PATTERN, temperature))
            append("°C")

            // Smart time estimation
            val timeEstimation = calculateTimeEstimation(status, amperage)
            if (timeEstimation.isNotEmpty()) {
                append(" • ")
                append(timeEstimation)
            }
        }

        // Cache the result
        lastFormattedContent = content
        lastContentHash = currentHash

        Log.v(TAG, "Generated unified notification content: $content")
        return content
    }
    
    /**
     * Optimized power calculation using the standard formula: Power = (Voltage × Current) / 1,000,000
     * Extracted to a separate method for reusability and testing.
     */
    private fun calculatePowerWatts(currentMicroAmperes: Long, voltageMillivolts: Int): Float {
        return (currentMicroAmperes / 1000.0f) * (voltageMillivolts / 1000.0f) / 1000.0f
    }

    /**
     * Calculates smart time estimation based on current charging state and target percentage.
     * Returns time to target percentage or time to full charge.
     */
    private fun calculateTimeEstimation(status: CoreBatteryStatus, currentMa: Float): String {
        if (!status.isCharging || currentMa <= 0) {
            return "" // No estimation for discharging or invalid current
        }

        val currentPercentage = status.percentage
        val targetPercentage = appRepository.getChargeAlarmPercent()

        // Determine target: user-set target or 100% if already reached target
        val finalTarget = if (targetPercentage > 0 && currentPercentage < targetPercentage) {
            targetPercentage
        } else {
            100 // Time to full charge
        }

        if (currentPercentage >= finalTarget) {
            return "" // Already at or above target
        }

        // Estimate time based on current charging rate
        // Assume average battery capacity of 4000mAh (can be made configurable)
        val estimatedBatteryCapacityMah = 4000f
        val percentageToCharge = finalTarget - currentPercentage
        val energyNeededMah = (percentageToCharge / 100f) * estimatedBatteryCapacityMah

        // Calculate time in hours, accounting for charging efficiency (assume 85%)
        val chargingEfficiency = 0.85f
        val timeHours = energyNeededMah / (currentMa * chargingEfficiency)

        return formatTimeEstimation(timeHours, finalTarget)
    }

    /**
     * Formats time estimation into human-readable format.
     */
    private fun formatTimeEstimation(timeHours: Float, targetPercentage: Int): String {
        if (timeHours <= 0 || timeHours > 24) {
            return "" // Invalid or unrealistic time
        }

        val hours = timeHours.toInt()
        val minutes = ((timeHours - hours) * 60).toInt()

        val targetText = if (targetPercentage == 100) "full" else "${targetPercentage}%"

        return when {
            hours > 0 && minutes > 0 -> "${hours}h ${minutes}m to $targetText"
            hours > 0 -> "${hours}h to $targetText"
            minutes > 0 -> "${minutes}m to $targetText"
            else -> "< 1m to $targetText"
        }
    }

    /**
     * Updates the foreground notification with current battery status.
     * Implements 5-second refresh rate for real-time updates, but allows immediate updates for initial data.
     *
     * @param status Current battery status to display
     * @param forceUpdate Force immediate update regardless of timing
     */
    private fun updateForegroundNotification(status: CoreBatteryStatus, forceUpdate: Boolean = false) {
        val now = System.currentTimeMillis()

        // Update notification every 5 seconds for real-time feel, or force update
        if (forceUpdate || now - lastNotificationUpdateTime >= NOTIFICATION_UPDATE_INTERVAL_MS) {
            val notification = createServiceNotification(status)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)
            lastNotificationUpdateTime = now

            Log.v(TAG, "Notification updated (forced: $forceUpdate, interval: ${now - lastNotificationUpdateTime}ms)")
        }
    }

    /**
     * Starts monitoring battery status by registering a BroadcastReceiver
     * for ACTION_BATTERY_CHANGED and emits the initial battery status.
     */
    private fun startMonitoring() {
        if (batteryEventReceiver != null) {
            Log.w(TAG, "Battery monitoring already started")
            return
        }

        Log.d(TAG, "Starting battery status monitoring")

        // Create and register the broadcast receiver
        batteryEventReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                if (intent?.action == Intent.ACTION_BATTERY_CHANGED) {
                    Log.v(TAG, "Battery status changed - processing intent")

                    try {
                        val newStatus = extractBatteryStatus(intent)

                        // Enhanced logging for health fragment debugging
                        Log.d(TAG, "BATTERY_UPDATE: === NEW BATTERY STATUS DETECTED ===")
                        Log.d(TAG, "BATTERY_UPDATE: Status details:")
                        Log.d(TAG, "BATTERY_UPDATE:   Percentage: ${newStatus.percentage}%")
                        Log.d(TAG, "BATTERY_UPDATE:   Charging: ${newStatus.isCharging}")
                        Log.d(TAG, "BATTERY_UPDATE:   Current: ${newStatus.currentMicroAmperes}µA")
                        Log.d(TAG, "BATTERY_UPDATE:   Temperature: ${newStatus.temperatureCelsius}°C")
                        Log.d(TAG, "BATTERY_UPDATE:   Timestamp: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(newStatus.timestampEpochMillis))}")

                        coreBatteryStatsProvider.updateStatus(newStatus)

                        // Update the notification with new status (force update for real-time data)
                        updateForegroundNotification(newStatus, forceUpdate = true)

                        Log.d(TAG, "BATTERY_UPDATE: ✅ Status updated and emitted to all observers")
                        Log.d(TAG, "BATTERY_UPDATE: This will trigger health recalculation in HealthRepository")
                        Log.d(TAG, "Battery status updated and emitted: $newStatus")
                    } catch (e: Exception) {
                        Log.e(TAG, "BATTERY_UPDATE: ❌ Error processing battery status change", e)

                        // Emit default status on error to maintain data flow
                        val defaultStatus = createDefaultStatus()
                        coreBatteryStatsProvider.updateStatus(defaultStatus)
                        Log.w(TAG, "BATTERY_UPDATE: Emitted default status to maintain data flow")
                    }
                }
            }
        }

        // Register the receiver for battery change events
        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        registerReceiver(batteryEventReceiver, filter)

        Log.d(TAG, "Battery event receiver registered")

        // Emit initial battery status immediately
        try {
            val initialStatus = getInitialBatteryStatus()
            coreBatteryStatsProvider.updateStatus(initialStatus)
            updateForegroundNotification(initialStatus, forceUpdate = true)
            Log.d(TAG, "Initial battery status emitted: $initialStatus")
        } catch (e: Exception) {
            Log.e(TAG, "Error getting initial battery status", e)
            val defaultStatus = createDefaultStatus()
            coreBatteryStatsProvider.updateStatus(defaultStatus)
            updateForegroundNotification(defaultStatus, forceUpdate = true)
        }
    }

    /**
     * Stops monitoring and shuts down the service.
     * Unregisters the broadcast receiver and stops the foreground service.
     */
    private fun stopMonitoringAndService() {
        Log.d(TAG, "Stopping battery monitoring and service")

        // Unregister the broadcast receiver if it exists
        batteryEventReceiver?.let { receiver ->
            try {
                unregisterReceiver(receiver)
                Log.d(TAG, "Battery event receiver unregistered")
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Receiver was not registered", e)
            }
            batteryEventReceiver = null
        }

        // Stop foreground service
        stopForeground(STOP_FOREGROUND_REMOVE)

        // Stop the service
        stopSelf()

        Log.d(TAG, "CoreBatteryStatsService stopped")
    }

    /**
     * Retrieves the current battery status for immediate use.
     * This method queries the system directly to get the latest battery information.
     *
     * @return Current CoreBatteryStatus
     */
    private fun getInitialBatteryStatus(): CoreBatteryStatus {
        Log.d(TAG, "Getting initial battery status")

        // Get current battery status using registerReceiver with null receiver
        val batteryIntent = registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))

        return if (batteryIntent != null) {
            extractBatteryStatus(batteryIntent)
        } else {
            Log.w(TAG, "Could not get battery intent, using default status")
            createDefaultStatus()
        }
    }

    /**
     * Extracts battery status information from an ACTION_BATTERY_CHANGED Intent.
     * Parses all relevant battery data and creates a CoreBatteryStatus object.
     *
     * @param intent The ACTION_BATTERY_CHANGED intent containing battery data
     * @return CoreBatteryStatus object with extracted data
     */
    private fun extractBatteryStatus(intent: Intent): CoreBatteryStatus {
        Log.v(TAG, "Extracting battery status from intent")

        // Extract basic battery information
        val level = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        val percentage = if (level >= 0 && scale > 0) {
            (level * 100 / scale).coerceIn(0, 100)
        } else {
            0
        }

        // Extract charging status
        val status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
        val isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                        status == BatteryManager.BATTERY_STATUS_FULL

        // Extract plugged source
        val pluggedSource = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0)

        // Extract voltage (in millivolts)
        val voltageMillivolts = intent.getIntExtra(BatteryManager.EXTRA_VOLTAGE, 0)

        // Extract temperature (in tenths of degrees Celsius, convert to Celsius)
        val temperatureTenths = intent.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0)
        val temperatureCelsius = temperatureTenths / 10.0f

        // Get current from BatteryManager (in microamperes)
        val currentMicroAmperes = try {
            val rawCurrentValue = batteryManager.getLongProperty(BatteryManager.BATTERY_PROPERTY_CURRENT_NOW)
            // Check if value is small, assume it's mA and convert to µA
            if (kotlin.math.abs(rawCurrentValue) < 5000) {
                Log.d(TAG, "Small current value detected: $rawCurrentValue mA, converting to µA")
                rawCurrentValue * 1000
            } else {
                rawCurrentValue
            }
        } catch (e: Exception) {
            Log.w(TAG, "Could not get current from BatteryManager", e)
            0L
        }

        val timestamp = System.currentTimeMillis()

        val extractedStatus = CoreBatteryStatus(
            percentage = percentage,
            isCharging = isCharging,
            pluggedSource = pluggedSource,
            currentMicroAmperes = currentMicroAmperes,
            voltageMillivolts = voltageMillivolts,
            temperatureCelsius = temperatureCelsius,
            timestampEpochMillis = timestamp
        )

        Log.d(TAG, "Extracted battery status: " +
            "level=$level/$scale ($percentage%), " +
            "status=$status (charging=$isCharging), " +
            "plugged=$pluggedSource, " +
            "voltage=${voltageMillivolts}mV, " +
            "temp=${temperatureCelsius}°C, " +
            "current=${currentMicroAmperes}µA")

        return extractedStatus
    }

    /**
     * Creates a default CoreBatteryStatus when real data is unavailable.
     * This ensures the application always has a valid status to work with.
     *
     * @return Default CoreBatteryStatus with safe fallback values
     */
    private fun createDefaultStatus(): CoreBatteryStatus {
        Log.w(TAG, "Creating default battery status")
        return CoreBatteryStatus.createDefault()
    }
}
